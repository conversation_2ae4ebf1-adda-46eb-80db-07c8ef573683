"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, XCircle, Eye, RefreshCw } from "lucide-react"

interface AIResponse {
  content: string
  language: string
  blockId?: string | null
  type: 'block' | 'general' | 'header_footer'
  originalPrompt?: string
  blockVariables?: Record<string, string>
}

interface AIConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  aiResponse: AIResponse | null
  onApply: () => void
  onDiscard: () => void
  onTryAgain: () => void
}

export function AIConfirmationDialog({ 
  open, 
  onOpenChange, 
  aiResponse, 
  onApply, 
  onDiscard, 
  onTryAgain 
}: AIConfirmationDialogProps) {
  
  const getLanguageDisplay = (langCode: string) => {
    const languageMap: Record<string, { display: string; flag: string }> = {
      'es': { display: 'Espanyol', flag: '🇪🇸' },
      'ca': { display: 'Català', flag: '🏴󠁥󠁳󠁣󠁴󠁿' },
      'fr': { display: 'Francès', flag: '🇫🇷' },
      'en': { display: 'Anglès', flag: '🇬🇧' }
    }
    return languageMap[langCode] || { display: langCode.toUpperCase(), flag: '🌐' }
  }

  const handleApply = () => {
    onApply()
    onOpenChange(false)
  }

  const handleDiscard = () => {
    onDiscard()
    onOpenChange(false)
  }

  const handleTryAgain = () => {
    onTryAgain()
    onOpenChange(false)
  }

  if (!aiResponse) return null

  const { display: languageDisplay, flag: languageFlag } = getLanguageDisplay(aiResponse.language)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-primary" />
            Vista prèvia del contingut generat per IA
          </DialogTitle>
          <DialogDescription>
            Revisa el contingut generat abans d'aplicar-lo al {aiResponse.type === 'block' ? 'bloc' : 'butlletí'}.
          </DialogDescription>
        </DialogHeader>

        {/* Scrollable Content Area */}
        <ScrollArea className="flex-1 pr-4">
          <div className="space-y-4 pb-4">
            {/* Response Metadata */}
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="flex items-center gap-1">
                <span>{languageFlag}</span>
                <span>{languageDisplay}</span>
              </Badge>
              <Badge variant={aiResponse.type === 'block' ? 'default' : 'outline'}>
                {aiResponse.type === 'block' ? 'Bloc específic' : 'General'}
              </Badge>
            </div>

            {/* Original Prompt (if provided) */}
            {aiResponse.originalPrompt && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Instruccions originals</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-sm text-muted-foreground italic">
                    "{aiResponse.originalPrompt}"
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Generated Content Preview */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Contingut generat</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                {aiResponse.blockVariables ? (
                  // Display block variables in a structured way with inner scroll
                  <ScrollArea className="h-[350px] w-full rounded-md border">
                    <div className="space-y-3 p-4">
                      {Object.entries(aiResponse.blockVariables).map(([variableName, value]) => (
                        <div key={variableName} className="border rounded-md p-3 bg-background">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline" className="text-xs">
                              {variableName}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded break-words whitespace-pre-wrap">
                            {value}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                ) : (
                  // Display HTML content for non-block responses
                  <ScrollArea className="h-[350px] w-full rounded-md border">
                    <div className="p-4">
                      <div
                        className="prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{ __html: aiResponse.content }}
                      />
                    </div>
                  </ScrollArea>
                )}
              </CardContent>
            </Card>

            {/* Warning Message */}
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                <strong>Atenció:</strong> Aplicar aquests canvis substituirà el contingut actual.
                Assegura't que el contingut generat és adequat abans de continuar.
              </p>
            </div>
          </div>
        </ScrollArea>

        {/* Fixed Footer */}
        <DialogFooter className="flex-shrink-0 border-t pt-4 mt-4">
          <div className="flex flex-col sm:flex-row gap-2 w-full">
            <div className="flex gap-2 flex-1">
              <Button
                variant="outline"
                onClick={handleTryAgain}
                className="flex-1"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Provar de nou
              </Button>
              <Button
                variant="destructive"
                onClick={handleDiscard}
                className="flex-1"
              >
                <XCircle className="mr-2 h-4 w-4" />
                Descartar
              </Button>
            </div>
            <Button
              onClick={handleApply}
              className="sm:w-auto bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              Aplicar canvis
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
