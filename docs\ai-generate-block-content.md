# AI Generate Block Content Feature

## Overview

The AI Generate Block Content feature allows users to automatically generate content for newsletter blocks using AI. This feature integrates with the backend API endpoint `POST /openai/generate-block-content/` to generate variable content based on user prompts.

## Implementation Details

### API Integration

**Endpoint:** `POST {{url}}openai/generate-block-content/`

**Request Format:**
```json
{
  "language": "ca",
  "prompt": "Promocionar 10% descuento forfaits que ya se venden online",
  "variables": {
    "text_titulo": "Ja pots comprar el teu forfait online!",
    "text_paragraf": "Descobreix les novetats..."
  }
}
```

**Response Format:**
```json
{
  "text_titulo": "Aprofita un 10% de descompte en forfaits comprats online!",
  "text_paragraf": "Compra ara el teu forfait a la nostra plataforma online i gaudeix d'un 10% de descompte exclusiu. No et perdis aquesta oferta i prepara't per una temporada espectacular a la neu!"
}
```

### Frontend Implementation

#### 1. Hook Updates (`hooks/use-ai-api.ts`)

- Added `GenerateBlockContentRequest` and `GenerateBlockContentResponse` interfaces
- Added `generateBlockContent` function to the `useAIApi` hook
- The function makes a POST request to the generate-block-content endpoint
- Includes proper error handling and loading states

#### 2. AI Dialog Updates (`components/newsletter/ai-dialog.tsx`)

- Modified `handleSubmit` to detect when working with blocks
- Extracts current variable values from the block for the selected language
- Calls the new `generateBlockContent` function instead of the generic `generateContent`
- Transforms the response to include `blockVariables` for easier processing

#### 3. Newsletter Builder Updates (`app/newsletter/builder/[id]/page.tsx`)

- Updated `handleApplyAIChanges` to handle block content generation
- When applying AI changes for blocks, it updates the block's variable values with the generated content
- Preserves existing variable values for other languages
- Updates the `last_edited_at` timestamp and sets unsaved changes flag

#### 4. AI Confirmation Dialog Updates (`components/newsletter/ai-confirmation-dialog.tsx`)

- Updated the `AIResponse` interface to include `blockVariables`
- Modified the content display to show generated variables in a structured format
- Each variable is displayed with its name and generated value

## User Flow

1. **Open AI Dialog**: User clicks the AI button on a newsletter block
2. **Enter Prompt**: User enters instructions for content generation (optional)
3. **Select Language**: User selects the target language for generation
4. **Generate Content**: System extracts current variable values and sends them to the API
5. **Preview Results**: User sees the generated content organized by variable name
6. **Apply Changes**: User can apply the generated content to update the block variables

## Features

- **Language Support**: Supports ca, es, fr, en languages
- **Variable Extraction**: Automatically extracts current variable values from blocks
- **Structured Preview**: Shows generated content organized by variable names
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Loading States**: Visual feedback during API calls
- **Undo Support**: Changes are applied to local state and can be discarded before saving

## Technical Notes

- The feature only works with blocks that have variables
- Current variable values are sent as context to help the AI generate relevant content
- Generated content replaces the variable values for the selected language only
- Other language values are preserved
- The implementation includes debug logging for troubleshooting

## Future Enhancements

- Add support for batch generation across multiple languages
- Include HTML content context in addition to variable values
- Add templates or presets for common content types
- Implement content validation and suggestions
