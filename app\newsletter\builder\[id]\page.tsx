"use client"

import { useState, useEffect, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { NextPage } from 'next'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Settings, Eye, Send, Bot, Save, AlertCircle, Sparkles, Languages, RefreshCwOff, RefreshCw, Globe } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Spinner } from '@/components/ui/shadcn-io/spinner'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import Link from 'next/link'
import { toast } from 'sonner'

// Import our new components
import { useNewsletterBuilder } from '@/hooks/use-newsletter-builder'
import { BlockAccordion } from '@/components/newsletter/block-accordion'
import { HTMLPreview } from '@/components/newsletter/html-preview'
import { AIDialog } from '@/components/newsletter/ai-dialog'
import { AIConfirmationDialog } from '@/components/newsletter/ai-confirmation-dialog'
import { TranslateDialog } from '@/components/newsletter/translate-dialog'
import { TranslatePreviewDialog } from '@/components/newsletter/translate-preview-dialog'
import { TranslateBlockDialog } from '@/components/newsletter/translate-block-dialog'
import { TranslateBlockPreviewDialog } from '@/components/newsletter/translate-block-preview-dialog'
import { NewsletterBlock, NewsletterBuilderData, NewsletterHeaderFooter } from '@/types/newsletter'
import { Block } from '@/types/block'
import { LanguageProvider, useLanguageContext } from '@/contexts/language-context'

interface Props { }

// Language Selector Component for Header
function HeaderLanguageSelector() {
  const { selectedLanguage, setSelectedLanguage, languages, loading } = useLanguageContext()

  const getLanguageDisplay = (langCode: string) => {
    const languageMap: Record<string, { display: string; color: string }> = {
      'es': { display: 'Espanyol', color: 'text-red-500' },
      'ca': { display: 'Català', color: 'text-yellow-500' },
      'fr': { display: 'Francès', color: 'text-blue-500' },
      'en': { display: 'Anglès', color: 'text-green-500' }
    }
    return languageMap[langCode] || { display: langCode.toUpperCase(), color: 'text-gray-500' }
  }

  if (loading) {
    return (
      <Select disabled>
        <SelectTrigger className="w-32">
          <SelectValue placeholder="Carregant..." />
        </SelectTrigger>
      </Select>
    )
  }

  return (
    <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
      <SelectTrigger className="w-32">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {['ca', 'es', 'fr', 'en'].map((langCode) => {
          const { display, color } = getLanguageDisplay(langCode)
          return (
            <SelectItem key={langCode} value={langCode}>
              <div className="flex items-center gap-2">
                <Globe className={`h-3 w-3 ${color}`} />
                <span>{display}</span>
              </div>
            </SelectItem>
          )
        })}
      </SelectContent>
    </Select>
  )
}

const NewsletterBuilderPage: NextPage<Props> = ({ }) => {
  const params = useParams()
  const newsletterId = params.id as string

  // State for AI dialogs
  const [aiDialogOpen, setAiDialogOpen] = useState(false)
  const [aiConfirmationOpen, setAiConfirmationOpen] = useState(false)
  const [selectedBlock, setSelectedBlock] = useState<NewsletterBlock | null>(null)
  const [selectedHeaderFooter, setSelectedHeaderFooter] = useState<NewsletterHeaderFooter | null>(null)
  const [aiDebugInfo, setAiDebugInfo] = useState<Record<string, any> | undefined>()
  const [aiResponse, setAiResponse] = useState<any>(null)

  // State for Translation dialogs
  const [translateDialogOpen, setTranslateDialogOpen] = useState(false)
  const [translatePreviewOpen, setTranslatePreviewOpen] = useState(false)
  const [translationResponse, setTranslationResponse] = useState<any>(null)
  const [targetLanguage, setTargetLanguage] = useState<string>("")

  // State for Block Translation dialogs
  const [translateBlockDialogOpen, setTranslateBlockDialogOpen] = useState(false)
  const [translateBlockPreviewOpen, setTranslateBlockPreviewOpen] = useState(false)
  const [blockTranslationResponse, setBlockTranslationResponse] = useState<any>(null)
  const [selectedBlockForTranslation, setSelectedBlockForTranslation] = useState<NewsletterBlock | null>(null)

  // Local editing state
  const [localNewsletterData, setLocalNewsletterData] = useState<NewsletterBuilderData | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isSynced, setIsSynced] = useState(false)
  const [saving, setSaving] = useState(false)

  // Newsletter data hook
  const {
    newsletterData,
    loading,
    error,
    refetch,
    updateNewsletter,
    updating,
    updateError
  } = useNewsletterBuilder({ newsletterId })

  // Sync local data with fetched data
  useEffect(() => {
    if (newsletterData && !localNewsletterData) {
      setLocalNewsletterData(JSON.parse(JSON.stringify(newsletterData))) // Deep copy
      setIsSynced(!(newsletterData.status === 'draft'))
    }
  }, [newsletterData, localNewsletterData])

  // Use local data for display, fallback to fetched data
  const displayData = localNewsletterData || newsletterData

  const handleSave = useCallback(async () => {
    if (!displayData || !hasUnsavedChanges) return

    setSaving(true)
    try {
      await updateNewsletter({
        newsletter_parent_id: newsletterId,
        name: displayData.name,
        status: displayData.status,
        nl_blocks: displayData.nl_blocks,
        headers: displayData.headers,
        footers: displayData.footers
      })

      toast.success('Newsletter saved successfully')
      setHasUnsavedChanges(false)
      refetch() // Refresh data from server
    } catch (err) {
      console.error('Error saving newsletter:', err)
    } finally {
      setSaving(false)
    }
  }, [displayData, hasUnsavedChanges, newsletterId, updateNewsletter, refetch])

  // Warn about unsaved changes when navigating away
  const handleBeforeUnload = useCallback((e: BeforeUnloadEvent) => {
    if (hasUnsavedChanges) {
      e.preventDefault()
      e.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
      return 'You have unsaved changes. Are you sure you want to leave?'
    }
  }, [hasUnsavedChanges])

  useEffect(() => {
    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [handleBeforeUnload])

  // Keyboard shortcut for saving (Ctrl+S)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        if (hasUnsavedChanges && !saving) {
          handleSave()
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [hasUnsavedChanges, saving, handleSave])

  const handleBlocksReorder = (reorderedBlocks: NewsletterBlock[]) => {
    if (!displayData) return

    const updatedBlocks = reorderedBlocks.map((block, index) => ({
      ...block,
      order_position: index + 1
    }))

    setLocalNewsletterData({
      ...displayData,
      nl_blocks: updatedBlocks
    })
    setHasUnsavedChanges(true)
  }

  const handleBlockAIClick = (block: NewsletterBlock) => {
    setSelectedBlock(block)
    setSelectedHeaderFooter(null)
    setAiDebugInfo({
      blockId: block.id,
      blockName: block.name,
      orderPosition: block.order_position,
      isVisible: block.is_visible,
      variableCount: block.variable_values?.length || 0,
      variables: block.variable_values?.map(v => ({
        name: v.name,
        type: v.variable_type_display_name,
        hasValues: Object.values(v.value).some(val => val.trim() !== '')
      })) || []
    })
    setAiDialogOpen(true)
  }

  const handleGlobalAIClick = () => {
    setSelectedBlock(null)
    setSelectedHeaderFooter(null)
    setAiDebugInfo({
      newsletterId,
      totalBlocks: newsletterData?.nl_blocks?.length || 0,
      visibleBlocks: newsletterData?.nl_blocks?.filter(b => b.is_visible)?.length || 0,
      brand: newsletterData?.brand_name
    })
    setAiDialogOpen(true)
  }

  const handleAddBlock = (block: Block) => {
    if (!displayData) return

    // Convert Block to NewsletterBlock format
    const newNewsletterBlock: NewsletterBlock = {
      id: block.id,
      name: block.name,
      description: block.description,
      html_content: block.html_content,
      order_position: displayData.nl_blocks.length + 1,
      is_visible: true,
      variable_values: block.variables.map(variable => ({
        id: variable.id,
        variable_type_id: variable.variable_type,
        variable_type_name: variable.name,
        variable_type_display_name: variable.name,
        variable_type_field_type: 'text', // Default field type
        name: variable.name,
        value: variable.default_value
      })),
      last_edited_by: null,
      last_edited_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      element_type_display: 'Block'
    }

    const updatedBlocks = [...displayData.nl_blocks, newNewsletterBlock]

    setLocalNewsletterData({
      ...displayData,
      nl_blocks: updatedBlocks
    })
    setHasUnsavedChanges(true)
    toast.success(`Block "${block.name}" added to newsletter`)
  }

  const handleVariableChange = (blockId: string, variableId: string, language: string, value: string) => {
    if (!displayData) return

    const updatedBlocks = displayData.nl_blocks.map(block => {
      if (block.id === blockId) {
        const updatedVariables = block.variable_values.map(variable => {
          if (variable.id === variableId) {
            return {
              ...variable,
              value: {
                ...variable.value,
                [language]: value
              }
            }
          }
          return variable
        })
        return { ...block, variable_values: updatedVariables }
      }
      return block
    })

    setLocalNewsletterData({
      ...displayData,
      nl_blocks: updatedBlocks
    })
    setHasUnsavedChanges(true)
  }

  const handleHtmlContentChange = (blockId: string, htmlContent: string) => {
    if (!displayData) return

    const updatedBlocks = displayData.nl_blocks.map(block => {
      if (block.id === blockId) {
        return { ...block, html_content: htmlContent }
      }
      return block
    })

    setLocalNewsletterData({
      ...displayData,
      nl_blocks: updatedBlocks
    })
    setHasUnsavedChanges(true)
  }

  const handleHeadersReorder = (reorderedHeaders: NewsletterHeaderFooter[]) => {
    if (!displayData) return

    setLocalNewsletterData({
      ...displayData,
      headers: reorderedHeaders
    })
    setHasUnsavedChanges(true)
  }

  const handleFootersReorder = (reorderedFooters: NewsletterHeaderFooter[]) => {
    if (!displayData) return

    setLocalNewsletterData({
      ...displayData,
      footers: reorderedFooters
    })
    setHasUnsavedChanges(true)
  }

  const handleHeaderFooterAIClick = (headerFooter: NewsletterHeaderFooter, type: 'header' | 'footer') => {
    setSelectedBlock(null) // Clear any selected block
    setSelectedHeaderFooter(headerFooter)
    setAiDebugInfo({
      type: 'header_footer',
      headerFooterId: headerFooter.id,
      headerFooterType: type,
      name: headerFooter.name,
      newsletterId,
      brand: displayData?.brand_name
    })
    setAiDialogOpen(true)
  }

  // AI Response Handlers
  const handleAIResponse = (response: any, language: string) => {
    setAiResponse(response)
    setAiConfirmationOpen(true)
  }

  const handleApplyAIChanges = () => {
    if (!aiResponse || !displayData) return

    // Handle block content generation
    if (aiResponse.type === 'block' && aiResponse.blockId && aiResponse.blockVariables) {
      const updatedBlocks = displayData.nl_blocks.map(block => {
        if (block.id === aiResponse.blockId) {
          // Update the block's variable values with the AI-generated content
          const updatedVariables = block.variable_values.map(variable => {
            const generatedValue = aiResponse.blockVariables[variable.name]
            if (generatedValue !== undefined) {
              return {
                ...variable,
                value: {
                  ...variable.value,
                  [aiResponse.language]: generatedValue
                }
              }
            }
            return variable
          })

          return {
            ...block,
            variable_values: updatedVariables,
            last_edited_at: new Date().toISOString()
          }
        }
        return block
      })

      setLocalNewsletterData({
        ...displayData,
        nl_blocks: updatedBlocks
      })
      setHasUnsavedChanges(true)

      toast.success('Contingut del bloc generat amb IA aplicat correctament!')
    } else {
      // Handle other types of AI content (header/footer, general)
      console.log('Applying AI changes for non-block content:', aiResponse)
      toast.success('Canvis d\'IA aplicats correctament!')
    }

    setAiResponse(null)
    setAiConfirmationOpen(false)
  }

  const handleDiscardAIChanges = () => {
    setAiResponse(null)
    setAiConfirmationOpen(false)
  }

  const handleTryAIAgain = () => {
    setAiConfirmationOpen(false)
    setAiDialogOpen(true)
  }

  // Translation Response Handlers
  const handleTranslateResponse = (response: any, baseLanguage: string) => {
    setTranslationResponse(response)
    setTargetLanguage(baseLanguage)
    setTranslatePreviewOpen(true)
  }

  const handleApplyTranslation = () => {
    if (!translationResponse?.newsletter) return

    // Apply the translation to the current newsletter
    setLocalNewsletterData(translationResponse.newsletter)
    setHasUnsavedChanges(true)

    // Close dialogs and reset state
    setTranslationResponse(null)
    setTranslatePreviewOpen(false)
    setTargetLanguage("")

    toast.success('Translation applied successfully')
  }

  const handleRedoTranslation = () => {
    setTranslatePreviewOpen(false)
    setTranslateDialogOpen(true)
  }

  const handleCancelTranslation = () => {
    setTranslationResponse(null)
    setTranslatePreviewOpen(false)
    setTargetLanguage("")
  }

  // Block Translation Response Handlers
  const handleBlockTranslateResponse = (response: any, sourceLanguage: string) => {
    setBlockTranslationResponse(response)
    setTranslateBlockPreviewOpen(true)
  }

  const handleApplyBlockTranslation = () => {
    if (!blockTranslationResponse || !selectedBlockForTranslation) return

    // Apply the translated block to replace the selected block
    const updatedBlocks = displayData?.nl_blocks.map(block => {
      if (block.id === selectedBlockForTranslation.id) {
        // Replace the block with the translated version
        return {
          ...blockTranslationResponse,
          // Keep the original order position and visibility
          order_position: block.order_position,
          is_visible: block.is_visible,
          last_edited_at: new Date().toISOString()
        }
      }
      return block
    }) || []

    if (displayData) {
      setLocalNewsletterData({
        ...displayData,
        nl_blocks: updatedBlocks
      })
      setHasUnsavedChanges(true)
    }

    // Close dialogs and reset state
    setBlockTranslationResponse(null)
    setTranslateBlockPreviewOpen(false)
    setSelectedBlockForTranslation(null)

    toast.success('Block translation applied successfully')
  }

  const handleRedoBlockTranslation = () => {
    setTranslateBlockPreviewOpen(false)
    setTranslateBlockDialogOpen(true)
  }

  const handleCancelBlockTranslation = () => {
    setBlockTranslationResponse(null)
    setTranslateBlockPreviewOpen(false)
    setSelectedBlockForTranslation(null)
  }

  const handleHeaderFooterVariableChange = (headerFooterId: string, variableId: string, language: string, value: string) => {
    if (!displayData) return

    // Update headers
    const updatedHeaders = displayData.headers.map(header => {
      if (header.id === headerFooterId) {
        const updatedVariables = header.variable_values.map(variable => {
          if (variable.id === variableId) {
            return {
              ...variable,
              value: {
                ...variable.value,
                [language]: value
              }
            }
          }
          return variable
        })
        return { ...header, variable_values: updatedVariables }
      }
      return header
    })

    // Update footers
    const updatedFooters = displayData.footers.map(footer => {
      if (footer.id === headerFooterId) {
        const updatedVariables = footer.variable_values.map(variable => {
          if (variable.id === variableId) {
            return {
              ...variable,
              value: {
                ...variable.value,
                [language]: value
              }
            }
          }
          return variable
        })
        return { ...footer, variable_values: updatedVariables }
      }
      return footer
    })

    setLocalNewsletterData({
      ...displayData,
      headers: updatedHeaders,
      footers: updatedFooters
    })
    setHasUnsavedChanges(true)
  }

  const handleHeaderFooterHtmlContentChange = (headerFooterId: string, htmlContent: string) => {
    if (!displayData) return

    // Update headers
    const updatedHeaders = displayData.headers.map(header => {
      if (header.id === headerFooterId) {
        return { ...header, html_content: htmlContent }
      }
      return header
    })

    // Update footers
    const updatedFooters = displayData.footers.map(footer => {
      if (footer.id === headerFooterId) {
        return { ...footer, html_content: htmlContent }
      }
      return footer
    })

    setLocalNewsletterData({
      ...displayData,
      headers: updatedHeaders,
      footers: updatedFooters
    })
    setHasUnsavedChanges(true)
  }

  const handleDeleteBlock = (blockId: string) => {
    if (!displayData) return

    // Find the block to be deleted
    const blockToDelete = displayData.nl_blocks.find(block => block.id === blockId)
    if (!blockToDelete) return

    // Remove the block from the list
    const remainingBlocks = displayData.nl_blocks.filter(block => block.id !== blockId)

    // Update order positions to maintain sequential numbering
    const updatedBlocks = remainingBlocks
      .sort((a, b) => a.order_position - b.order_position)
      .map((block, index) => ({
        ...block,
        order_position: index + 1
      }))

    setLocalNewsletterData({
      ...displayData,
      nl_blocks: updatedBlocks
    })
    setHasUnsavedChanges(true)

    // Show success toast
    toast.success(`Block "${blockToDelete.name}" has been deleted`)
  }

  const handleBlockTranslateClick = (block: NewsletterBlock) => {
    setSelectedBlockForTranslation(block)
    setTranslateBlockDialogOpen(true)
  }

  const handleHeaderFooterTranslateClick = (headerFooter: NewsletterHeaderFooter, type: 'header' | 'footer') => {
    // TODO: Implement translate functionality for headers/footers
    toast.info(`Translate functionality for ${type} "${headerFooter.name}"`)
  }





  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Spinner className="h-8 w-8 mx-auto mb-4" variant='infinite' />
            <p className="text-muted-foreground">Carregant...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
        <div className="flex justify-center">
          <Button onClick={refetch} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  if (!newsletterData) {
    return (
      <div className="p-6 space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Newsletter not found
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <LanguageProvider>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Newsletter Builder
              {hasUnsavedChanges && (
                <span className="ml-2 text-orange-500 text-lg">•</span>
              )}
            </h1>
            <p className="text-muted-foreground">
              {displayData?.name || 'Carregant...'} • {displayData?.brand_name || 'Carregant...'}
              <span className={`ml-4 text-sm ${isSynced ? 'text-green-600' : 'text-red-600'}`}>
                • {isSynced ? 'Sincronitzat' : 'No sincronitzat'}
              </span>
              {hasUnsavedChanges && (
                <span className="ml-2 text-orange-500 text-sm">• Canvis no desats</span>
              )}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <HeaderLanguageSelector />
          <Button
            variant="outline"
            size="sm"
          >
            {/* <RefreshCwOff className="h-4 w-4 mr-2" /> */}
            <RefreshCw className={"h-4 w-4 mr-2"} />
            Sincronitzar
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleSave}
            disabled={saving || !hasUnsavedChanges}
          >
            {saving ? (
              <Spinner className="h-4 w-4 mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Guardar
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {updateError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {updateError}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content - Two Panel Layout */}
      <div className="grid grid-cols-6 gap-6">
        {/* Left Panel - Block Management */}
        <div className="space-y-3 col-span-2">
          <BlockAccordion
            blocks={displayData?.nl_blocks || []}
            headers={displayData?.headers || []}
            footers={displayData?.footers || []}
            onBlocksReorder={handleBlocksReorder}
            onBlockAIClick={handleBlockAIClick}
            onBlockTranslateClick={handleBlockTranslateClick}
            onDeleteBlock={handleDeleteBlock}
            onHeadersReorder={handleHeadersReorder}
            onFootersReorder={handleFootersReorder}
            onHeaderFooterAIClick={handleHeaderFooterAIClick}
            onHeaderFooterTranslateClick={handleHeaderFooterTranslateClick}
            onAddBlock={handleAddBlock}
            onVariableChange={handleVariableChange}
            onHtmlContentChange={handleHtmlContentChange}
            onHeaderFooterVariableChange={handleHeaderFooterVariableChange}
            onHeaderFooterHtmlContentChange={handleHeaderFooterHtmlContentChange}
            brandName={displayData?.brand_name}
            brand={displayData?.brand}
          />
        </div>

        {/* Right Panel - HTML Preview */}
        <div className="col-span-4 space-y-6 min-w-[300px] sticky top-6 max-h-[calc(100vh-48px)] overflow-y-auto">
          <HTMLPreview
            blocks={displayData?.nl_blocks || []}
            headers={displayData?.headers || []}
            footers={displayData?.footers || []}
            className="h-full min-w-[600px]"
          />
        </div>
      </div>

      {/* Global translate Button */}
      <Button
        className="fixed bottom-6 right-20 shadow-lg transition-all duration-300 bg-white border border-blue-700 hover:scale-110 hover:bg-white px-4"
        onClick={() => setTranslateDialogOpen(true)}
        size={"icon"}
      >
        <Languages className="h-4 w-4 text-blue-600" />
      </Button>

      {/* Global AI Button */}
      <Button
        className="fixed bottom-6 right-6 shadow-lg transition-all duration-300 hover:bg-gradient-to-r hover:from-purple-500 hover:to-blue-500 hover:shadow-xl hover:shadow-blue-500/30 hover:scale-110 group animate-pulse-gradient"
        size="icon"
        onClick={handleGlobalAIClick}
        style={{
          background: "linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab)",
          backgroundSize: "400% 400%",
          animation: "gradient 15s ease infinite"
        }}
      >
        <Sparkles className="h-6 w-6 text-white group-hover:animate-pulse" />
      </Button>

      {/* AI Dialog */}
      <AIDialog
        open={aiDialogOpen}
        onOpenChange={setAiDialogOpen}
        block={selectedBlock}
        headerFooter={selectedHeaderFooter}
        newsletterId={newsletterId}
        onAIResponse={handleAIResponse}
        debugInfo={aiDebugInfo}
      />

      {/* AI Confirmation Dialog */}
      <AIConfirmationDialog
        open={aiConfirmationOpen}
        onOpenChange={setAiConfirmationOpen}
        aiResponse={aiResponse}
        onApply={handleApplyAIChanges}
        onDiscard={handleDiscardAIChanges}
        onTryAgain={handleTryAIAgain}
      />

      {/* Translation Dialog */}
      <TranslateDialog
        open={translateDialogOpen}
        onOpenChange={setTranslateDialogOpen}
        newsletterId={newsletterId}
        onTranslateResponse={handleTranslateResponse}
      />

      {/* Translation Preview Dialog */}
      <TranslatePreviewDialog
        open={translatePreviewOpen}
        onOpenChange={setTranslatePreviewOpen}
        translatedNewsletter={translationResponse?.newsletter}
        targetLanguage={targetLanguage}
        originalPrompt={translationResponse?.originalPrompt || ""}
        onReplace={handleApplyTranslation}
        onRedo={handleRedoTranslation}
        onCancel={handleCancelTranslation}
      />

      {/* Block Translation Dialog */}
      <TranslateBlockDialog
        open={translateBlockDialogOpen}
        onOpenChange={setTranslateBlockDialogOpen}
        block={selectedBlockForTranslation}
        onTranslateResponse={handleBlockTranslateResponse}
      />

      {/* Block Translation Preview Dialog */}
      <TranslateBlockPreviewDialog
        open={translateBlockPreviewOpen}
        onOpenChange={setTranslateBlockPreviewOpen}
        translatedBlock={blockTranslationResponse}
        translatedVariables={blockTranslationResponse?.variable_values || []}
        sourceLanguage={blockTranslationResponse?.source_language || ""}
        originalContext={blockTranslationResponse?.user_input || ""}
        onReplace={handleApplyBlockTranslation}
        onRedo={handleRedoBlockTranslation}
        onCancel={handleCancelBlockTranslation}
      />
      </div>
    </LanguageProvider>
  )
}

export default NewsletterBuilderPage
